import { isEmpty, isNil } from 'lodash-es';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { auditHubControllerGetCustomerRequestWithControlsOptions } from '@globals/api-sdk/queries';
import type { AuditHubCustomerRequestControlResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { AUDIT_HUB_DEFAULT_PARAMS } from './constants/audit-hub-default-params.constants';

export type CustomerRequestControlsListQuery = Required<
    Parameters<typeof auditHubControllerGetCustomerRequestWithControlsOptions>
>[0]['query'];

class AuditHubControlsController {
    currentAppliedFilters: CustomerRequestControlsListQuery | null = null;

    auditCustomerRequestControlsQuery = new ObservedQuery(
        auditHubControllerGetCustomerRequestWithControlsOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get auditCustomerRequestControls(): AuditHubCustomerRequestControlResponseDto[] {
        return this.auditCustomerRequestControlsQuery.data?.data ?? [];
    }

    get auditCustomerRequestControlsIsLoading(): boolean {
        return this.auditCustomerRequestControlsQuery.isLoading;
    }

    get auditCustomerRequestControlsTotal(): number {
        return this.auditCustomerRequestControlsQuery.data?.total ?? 0;
    }

    get auditCustomerRequestControlsCurrentPage(): number {
        return this.auditCustomerRequestControlsQuery.data?.page ?? 0;
    }

    get auditCustomerRequestControlsLimit(): number {
        return this.auditCustomerRequestControlsQuery.data?.limit ?? 0;
    }

    loadControlsPage = (dataTableParams?: FetchDataResponseParams): void => {
        const { getRequestId: requestId, auditorFrameworkId: auditId } =
            sharedCustomerRequestDetailsController;

        if (!auditId || !requestId) {
            return;
        }

        const params = isNil(dataTableParams)
            ? AUDIT_HUB_DEFAULT_PARAMS
            : dataTableParams;

        this.#loadAuditCustomerRequestControls(
            String(auditId),
            Number(requestId),
            params,
        );
    };

    #loadAuditCustomerRequestControls = (
        auditId: string,
        customerRequestId: number,
        params: FetchDataResponseParams,
    ): void => {
        const { pagination, globalFilter, sorting } = params;
        const { pageSize, page = 1 } = pagination;
        const { search } = globalFilter;

        const query: CustomerRequestControlsListQuery = {
            page,
            limit: pageSize,
            q: search ?? undefined,
        };

        if (isEmpty(sorting)) {
            query.sort = 'NAME';
            query.sortDir = 'ASC';
        } else {
            query.sort = sorting[0].id as 'NAME' | 'CODE';
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.currentAppliedFilters = query;

        this.auditCustomerRequestControlsQuery.load({
            query,
            path: {
                auditId,
                customerRequestId,
            },
        });
    };
}

export const sharedAuditHubControlsController =
    new AuditHubControlsController();
