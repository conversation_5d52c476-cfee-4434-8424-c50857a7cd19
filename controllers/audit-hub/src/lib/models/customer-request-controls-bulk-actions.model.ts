import type { CustomerRequestControlsListQuery } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';

export class CustomerRequestControlsBulkActionsModel {
    selectedControlIds: number[] = [];
    isAllRowsSelected = false;
    datatableRef: React.RefObject<DatatableRef>;
    appliedFilters: CustomerRequestControlsListQuery | null;
    isEvidencePackageDownloading = false;

    constructor(
        appliedFilters: CustomerRequestControlsListQuery | null,
        datatableRef: React.RefObject<DatatableRef>,
    ) {
        makeAutoObservable(this);
        this.appliedFilters = appliedFilters;
        this.datatableRef = datatableRef;
    }

    get bulkActions(): BulkAction[] {
        return [
            {
                actionType: 'button',
                id: 'download-button',
                typeProps: {
                    startIconName: 'Download',
                    label: t`Download Selection`,
                    level: 'tertiary',
                    onClick: () => {
                        when(
                            () => !this.isEvidencePackageDownloading,
                            () => {
                                this._downloadControls(this.selectedControlIds);
                            },
                        );
                    },
                },
            },
        ];
    }

    _downloadControls(controlIds: number[]): void {
        this.isEvidencePackageDownloading = true;
        sharedCustomerRequestDetailsController.generateRequestControlEvidencePackage(
            {
                selectedControlIds: controlIds,
                selectAll: this.isAllRowsSelected,
                q: this.appliedFilters?.q || undefined,
            },
        );
        when(
            () =>
                !sharedCustomerRequestDetailsController
                    .generateRequestControlEvidencePackageMutation.isPending,
            () => {
                this.isEvidencePackageDownloading = false;
            },
        );
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const selectedIds = Object.keys(selectedRows);

        this.selectedControlIds = selectedIds.map(Number);
        this.isAllRowsSelected = isAllRowsSelected;
    };
}
