import { useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { openLinkControlsModalWithWorkspace } from '@components/evidence-library';
import {
    sharedAuditHubController,
    sharedAuditHubControlsController,
} from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import type { DatatableRef } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { CustomerRequestControlsBulkActionsModel } from '../../../../controllers/audit-hub/src/lib/models/customer-request-controls-bulk-actions.model';
import { getEvidenceRequestDetailsControlsColumns } from './constants/evidence-controls-columns';
import { openControlDetailsPanel } from './helpers/open-control-details-panel.helper';

export const EvidenceRequestDetailsControlsView = observer((): JSX.Element => {
    const { downloadAllControls, updateRequestControls } =
        sharedCustomerRequestDetailsController;

    const {
        auditCustomerRequestControls,
        auditCustomerRequestControlsIsLoading,
        auditCustomerRequestControlsTotal,
        loadControlsPage,
        currentAppliedFilters,
    } = sharedAuditHubControlsController;

    const { auditByIdData } = sharedAuditHubController;
    const workspaceId = auditByIdData?.framework.productId;
    const { getRequestId: requestId, auditorFrameworkId: auditId } =
        sharedCustomerRequestDetailsController;

    const datatableRef = useRef<DatatableRef>(null);

    const { bulkActions, handleRowSelection, isEvidencePackageDownloading } =
        new CustomerRequestControlsBulkActionsModel(
            currentAppliedFilters,
            datatableRef,
        );

    return (
        <AppDatatable
            isRowSelectionEnabled
            getRowId={(row) => String(row.id)}
            imperativeHandleRef={datatableRef}
            isLoading={auditCustomerRequestControlsIsLoading}
            tableId="datatable-audit-hub-evidence-request-details-controls"
            data={auditCustomerRequestControls}
            total={auditCustomerRequestControlsTotal}
            data-testid="EvidenceRequestDetailsControlsView"
            data-id="U9j_XYCl"
            columns={getEvidenceRequestDetailsControlsColumns()}
            bulkActionDropdownItems={bulkActions}
            defaultColumnOptions={{
                minSize: 5,
            }}
            defaultPaginationOptions={{
                pageSizeOptions: [5, 10, 20, 50],
                pageSize: 10,
                pageIndex: 0,
            }}
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        isIconOnly: false,
                        label: t`Download all`,
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        isLoading: isEvidencePackageDownloading,
                        a11yLoadingLabel: t`Downloading evidence package`,
                        onClick: downloadAllControls,
                    },
                },
                {
                    actionType: 'button',
                    id: 'map-controls-button',
                    typeProps: {
                        label: t`Map controls`,
                        level: 'secondary',
                        onClick: () => {
                            const currentControlIds: number[] = [];

                            openLinkControlsModalWithWorkspace({
                                objectType: 'risk',
                                onConfirm: (selectedControls) => {
                                    const controlIds = selectedControls.map(
                                        (item) => item.controlData.id,
                                    );

                                    updateRequestControls(
                                        controlIds as number[],
                                    );
                                },
                                excludeControlIds: currentControlIds,
                                auditId: auditId || undefined,
                                productId: workspaceId,
                                excludeRequestId: requestId || undefined,
                            });
                        },
                    },
                },
            ]}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: t`Controls`,
                description: t`No controls were found`,
            }}
            onFetchData={loadControlsPage}
            onRowSelection={handleRowSelection}
            onRowClick={({ row }) => {
                openControlDetailsPanel(row.id as number, datatableRef);
            }}
        />
    );
});
