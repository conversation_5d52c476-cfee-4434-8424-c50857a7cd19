import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { panelController } from '@controllers/panel';
import type { DatatableRef } from '@cosmos/components/datatable';
import type { AuditHubCustomerRequestControlResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { openControlDetailsPanelPaginated } from '../helpers/open-control-details-panel.helper';

class ControlDetailsPanelController {
    constructor() {
        makeAutoObservable(this);
    }

    get currentControlId(): number | undefined {
        return panelController.queryParams.controlId as number | undefined;
    }

    get customerRequestControls(): AuditHubCustomerRequestControlResponseDto[] {
        return panelController.queryParams
            .controlsOfCurrentPage as AuditHubCustomerRequestControlResponseDto[];
    }

    get totalControls(): number {
        return panelController.queryParams.total as number;
    }

    get currentPage(): number {
        return panelController.queryParams.currentPage as number;
    }

    get limitPerPage(): number {
        return panelController.queryParams.limit as number;
    }

    get datatableReference(): React.RefObject<DatatableRef> {
        return panelController.queryParams
            .datatableRef as React.RefObject<DatatableRef>;
    }

    get panelQueryParams(): Record<string, unknown> {
        return panelController.queryParams;
    }

    get currentControlIndex(): number {
        return this.customerRequestControls.findIndex(
            (control) => Number(control.id) === this.currentControlId,
        );
    }

    get currentControlData(): AuditHubCustomerRequestControlResponseDto {
        return this.customerRequestControls[this.currentControlIndex];
    }

    get hasValidControlData(): boolean {
        return Boolean(this.currentControlData);
    }

    loadControlEvidences = (): void => {
        if (!this.currentControlId) {
            return;
        }
        sharedCustomerRequestDetailsController.loadControlEvidences(
            this.currentControlId,
        );
    };

    get canNavigateNext(): boolean {
        return (
            this.currentControlIndex < this.customerRequestControls.length - 1
        );
    }

    get canNavigatePrev(): boolean {
        return this.currentControlIndex > 0;
    }

    get currentControlPosition(): number {
        return (
            this.currentControlIndex +
            1 +
            (this.currentPage - 1) * this.limitPerPage
        );
    }

    get isThereNextDatatablePage(): boolean {
        const totalPages = Math.ceil(this.totalControls / this.limitPerPage);

        return this.currentPage < totalPages;
    }

    get isTherePreviousDatatablePage(): boolean {
        return this.currentPage > 1;
    }

    handleClosePanel = (): void => {
        panelController.closePanel();
    };

    handleNextPage = (): void => {
        if (!this.canNavigateNext && !this.isThereNextDatatablePage) {
            return;
        }

        if (!this.canNavigateNext && this.isThereNextDatatablePage) {
            this.datatableReference.current?.setPageIndex(this.currentPage + 1);

            return; // Refresh the panel with the new page data
        }

        const nextControl =
            this.customerRequestControls[this.currentControlIndex + 1];

        openControlDetailsPanelPaginated(
            Number(nextControl.id),
            1,
            this.panelQueryParams,
        );
    };

    handlePrevPage = (): void => {
        if (!this.canNavigatePrev && !this.isTherePreviousDatatablePage) {
            return;
        }

        if (!this.canNavigatePrev && this.isTherePreviousDatatablePage) {
            this.datatableReference.current?.setPageIndex(this.currentPage - 1);

            return; // Refresh the panel with the new page data
        }

        const prevControl =
            this.customerRequestControls[this.currentControlIndex - 1];

        openControlDetailsPanelPaginated(
            Number(prevControl.id),
            -1,
            this.panelQueryParams,
        );
    };
}

export const sharedControlDetailsPanelController =
    new ControlDetailsPanelController();
