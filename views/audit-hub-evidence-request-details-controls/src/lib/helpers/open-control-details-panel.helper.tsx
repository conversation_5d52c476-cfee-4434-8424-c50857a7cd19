import {
    sharedAuditHubController,
    sharedAuditHubControlsController,
} from '@controllers/audit-hub';
import { sharedAuditHubControlDetailsController } from '@controllers/audit-hub-control-details';
import { sharedControlOwnersController } from '@controllers/controls';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { panelController } from '@controllers/panel';
import type { DatatableRef } from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import { ControlDetailsPanel } from '../components/control-details-panel';

export const openControlDetailsPanel = action(
    (controlId: number, datatableRef: React.RefObject<DatatableRef>): void => {
        const { auditByIdData } = sharedAuditHubController;

        if (!auditByIdData) {
            throw new Error('Audit data is not available');
        }

        sharedAuditHubControlDetailsController.load(
            Number(controlId),
            auditByIdData.framework.productId,
        );
        sharedControlOwnersController.loadOwnersByControlId(Number(controlId));

        panelController.openPanel({
            id: 'control-details-panel',
            queryParams: {
                controlId,
                controlsOfCurrentPage:
                    sharedAuditHubControlsController.auditCustomerRequestControls,
                currentPage:
                    sharedAuditHubControlsController.auditCustomerRequestControlsCurrentPage,
                limit: sharedAuditHubControlsController.auditCustomerRequestControlsLimit,
                total: sharedAuditHubControlsController.auditCustomerRequestControlsTotal,
                datatableRef,
            },
            content: () => (
                <ControlDetailsPanel data-id="control-details-panel-content" />
            ),
        });
    },
);

export const updateControlDetailsPanel = action(
    (controlId: number, datatableRef: React.RefObject<DatatableRef>): void => {
        // Update panel only if it's open
        if (panelController.currentPanelId) {
            openControlDetailsPanel(controlId, datatableRef);
        }
    },
);

export const openControlDetailsPanelPaginated = action(
    (
        currentControlId: number | undefined,
        offset: number,
        initialQueryParams: Record<string, unknown>,
    ): void => {
        const { auditByIdData } = sharedAuditHubController;

        if (!auditByIdData) {
            throw new Error('Audit data is not available');
        }

        sharedAuditHubControlDetailsController.load(
            Number(currentControlId),
            auditByIdData.framework.productId,
        );
        sharedControlOwnersController.loadOwnersByControlId(
            Number(currentControlId),
        );

        sharedCustomerRequestDetailsController.loadControlEvidences(
            Number(currentControlId),
        );

        // This will be called by the pagination buttons
        // We'll need to get the controls list and find the next/prev control
        panelController.openPanel({
            id: 'control-details-panel',
            queryParams: {
                ...initialQueryParams,
                controlId: currentControlId,
                offset,
            },
            content: () => (
                <ControlDetailsPanel data-id="control-details-panel-content" />
            ),
        });
    },
);
